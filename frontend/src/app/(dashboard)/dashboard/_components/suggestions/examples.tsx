'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  RefreshCw,
  Search,
  Lock,
} from 'lucide-react';
import {
  SiGmail, SiNotion, SiLinear, SiHubspot, SiFigma, SiClickup, SiGooglesheets, SiGoogledocs,
  SiSlack, SiSalesforce, SiAirtable, SiZoom, SiReddit, SiGoogledrive
} from 'react-icons/si';
import { FaMicrosoft, FaTwitter } from 'react-icons/fa';
import {
  getPersonalizedPromptRecommendations,
  getPersonalizedPromptRecommendationsPostOAuth,
  getPromptsToDisplay,
  getMissingIntegrationAppKeys,
  PersonalizedPromptRecommendation
} from '@/lib/utils/personalized-prompt-system';
import { INTEGRATION_DISPLAY_NAMES } from '@/lib/utils/integration-checker';

type PromptExample = {
  title: string;
  query: string;
  integrations?: string[];
};

// Integration icon mapping
const integrationIcons: Record<string, React.ComponentType<any>> = {
  'gmail.com': SiGmail,
  'google.com': SiGooglesheets, // Default to Sheets for google.com
  'googledocs.com': SiGoogledocs,
  'notion.so': SiNotion,
  'linear.app': SiLinear,
  'hubspot.com': SiHubspot,
  'twitter.com': FaTwitter,
  'figma.com': SiFigma,
  'clickup.com': SiClickup,
  'apollo.io': Search, // Using Search icon as fallback for Apollo
  'microsoft.com': FaMicrosoft,
};

const allPrompts: PromptExample[] = [
  // Sales & Marketing Automation
  {
    title: 'End-to-End Lead Generation Pipeline',
    query: 'Find 20 VP Marketing contacts at Series A SaaS companies, enrich their data with Apollo, create a tracking sheet in Google Sheets, and draft personalized cold emails using our Notion messaging templates.',
    integrations: ['apollo.io', 'google.com', 'notion.so', 'gmail.com'],
  },
  {
    title: 'Social Media Intelligence & Response',
    query: 'Monitor Twitter mentions of our competitors, summarize sentiment in a Notion page, and draft response tweets for our brand account.',
    integrations: ['twitter.com', 'notion.so'],
  },
  {
    title: 'HubSpot Sales Sequence Automation',
    query: 'When a new lead fills out our contact form, create a HubSpot contact, add them to our nurture sequence, and notify the sales team via Gmail with their enriched profile.',
    integrations: ['hubspot.com', 'apollo.io', 'gmail.com', 'linear.app'],
  },

  // Product & Project Management
  {
    title: 'Cross-Platform Project Sync',
    query: 'Sync our Linear sprint progress with ClickUp tasks, update our product roadmap in Notion, and send a weekly status email to stakeholders.',
    integrations: ['linear.app', 'clickup.com', 'notion.so', 'google.com', 'gmail.com'],
  },
  {
    title: 'Design-to-Development Handoff',
    query: 'When a Figma design is marked "Ready for Dev", create Linear tickets with design specs, update our Notion design system, and notify the engineering team.',
    integrations: ['figma.com', 'linear.app', 'notion.so'],
  },
  {
    title: 'Customer Feedback Analysis',
    query: 'Analyze all HubSpot support tickets tagged "feature request" from this month, identify patterns, and create prioritized Linear epics with supporting data.',
    integrations: ['hubspot.com', 'notion.so', 'linear.app', 'google.com'],
  },

  // Operations & Productivity
  {
    title: 'Meeting Intelligence & Follow-up',
    query: 'Summarize today\'s product meeting notes from Notion, create action items in Linear, and send follow-up emails to all attendees with their specific tasks.',

    integrations: ['notion.so', 'linear.app', 'gmail.com', 'google.com'],
  },
  {
    title: 'Vendor Research & Procurement',
    query: 'Research top 5 customer support tools, compare pricing and features in a Google Sheet, create evaluation criteria in Notion, and schedule demos via Gmail.',

    integrations: ['google.com', 'notion.so', 'gmail.com', 'linear.app'],
  },

  {
    title: 'Financial Reporting & Analysis',
    query: 'Pull Q3 sales data from HubSpot, create financial projections in Excel, update our board deck in Google Docs, and send summary to investors.',

    integrations: ['hubspot.com', 'microsoft.com', 'google.com', 'gmail.com'],
  },

  // Advanced Multi-Platform Workflows
  {
    title: 'Customer Success Automation',
    query: 'When a HubSpot deal closes, create onboarding tasks in ClickUp, add customer to our success tracking sheet, send welcome sequence via Gmail, and create success metrics dashboard in Notion.',
    integrations: ['hubspot.com', 'clickup.com', 'google.com', 'gmail.com', 'notion.so', 'linear.app'],
  },
  {
    title: 'Content Marketing & SEO Automation',
    query: 'Research trending topics in our industry, create content calendar in Notion, draft blog outlines in Google Docs, and schedule social promotion tweets.',
    integrations: ['notion.so', 'google.com', 'linear.app', 'twitter.com'],
  },
  {
    title: 'Competitive Intelligence & Analysis',
    query: 'Monitor competitor product updates, pricing changes, and social media activity. Compile insights in Notion, update our positioning doc, and alert the strategy team.',
    integrations: ['notion.so', 'google.com', 'gmail.com'],
  },

  // Technical & Development Support
  {
    title: 'Bug Triage & Development Workflow',
    query: 'When a critical bug is reported in HubSpot, create a Linear ticket, notify the dev team via Gmail, update our status page in Notion, and track resolution progress.',
    integrations: ['hubspot.com', 'linear.app', 'gmail.com', 'notion.so'],
  },
  {
    title: 'Documentation & Knowledge Management',
    query: 'When new features are deployed, update our help docs in Notion, create training materials in Google Docs, and notify the support team with change summaries.',
    integrations: ['linear.app', 'notion.so', 'google.com', 'gmail.com'],
  },
];



// Individual card component with smooth animations
const ExampleCard = ({
  prompt,
  index,
  onSelectPrompt
}: {
  prompt: PromptExample;
  index: number;
  onSelectPrompt?: (query: string) => void;
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, {
    once: true,
    margin: "0px 0px -20% 0px"
  });

  // Helper function to get the first sentence from the query
  const getFirstSentence = (query: string) => {
    // Find the first sentence ending with '.', '!', or '?'
    const match = query.match(/^[^.!?]*[.!?]/);
    if (match) {
      return match[0].trim();
    }

    // If no sentence ending found, truncate at a reasonable length
    if (query.length <= 80) return query;
    const truncated = query.substring(0, 80);
    const lastSpace = truncated.lastIndexOf(' ');
    return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{
        opacity: 0,
        y: 20,
        scale: 0.95
      }}
      animate={isInView ? {
        opacity: 1,
        y: 0,
        scale: 1
      } : {
        opacity: 0,
        y: 20,
        scale: 0.95
      }}
      transition={{
        duration: 0.4,
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{
        scale: 1.02,
        y: -3,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
    >
      <Card
        className="cursor-pointer h-full bg-muted/50 dark:bg-muted/30 border border-border min-h-[45px] transition-shadow duration-300 hover:shadow-lg"
        onClick={() => onSelectPrompt && onSelectPrompt(prompt.query)}
      >
        <CardContent className="px-3 py-0.5 h-full flex flex-col space-y-0.5">
          {/* Quote snippet at the top - single line */}
          <div>
            <blockquote className="text-xs text-muted-foreground italic leading-tight truncate">
              "{getFirstSentence(prompt.query)}"
            </blockquote>
          </div>

          {/* Title */}
          <CardTitle className="font-semibold text-foreground text-sm leading-tight">
            {prompt.title}
          </CardTitle>

          {/* Integration icons tray - compact size */}
          {prompt.integrations && prompt.integrations.length > 0 && (
            <div className="flex items-center">
              {prompt.integrations.slice(0, 5).map((integration, idx) => {
                const IconComponent = integrationIcons[integration] || Search;
                return (
                  <div
                    key={integration}
                    className="relative flex items-center justify-center bg-background border border-border rounded-full shadow-sm p-1"
                    style={{
                      height: 24,
                      width: 24,
                      marginLeft: idx > 0 ? '-8px' : '0',
                      zIndex: prompt.integrations!.length - idx,
                    }}
                  >
                    <IconComponent className="text-muted-foreground" size={14} />
                  </div>
                );
              })}
              {prompt.integrations.length > 5 && (
                <div
                  className="flex items-center justify-center bg-muted border border-border rounded-full text-xs text-muted-foreground font-medium shadow-sm"
                  style={{
                    height: 24,
                    width: 24,
                    marginLeft: '-8px',
                    zIndex: 0,
                  }}
                >
                  +{prompt.integrations.length - 5}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Integration icon mapping for the original system
const getIntegrationIcon = (appKey: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'gmail': SiGmail,
    'google_sheets': SiGooglesheets,
    'google_calendar': SiGooglesheets, // Using sheets icon as fallback for calendar
    'google_docs': SiGoogledocs,
    'google_drive': SiGoogledrive,
    'notion': SiNotion,
    'slack': SiSlack,
    'teams': FaMicrosoft,
    'twitter': FaTwitter,
    'linear': SiLinear,
    'clickup': SiClickup,
    'hubspot': SiHubspot,
    'salesforce': SiSalesforce,
    'airtable': SiAirtable,
    'zoom': SiZoom,
    'outlook': FaMicrosoft, // Using Microsoft icon for Outlook
    'reddit': SiReddit,
    'findanyone': Search, // Using Search icon for FindAnyone
    // Legacy mappings for backward compatibility
    'google.com': SiGooglesheets,
    'notion.so': SiNotion,
    'linear.app': SiLinear,
    'hubspot.com': SiHubspot,
    'twitter.com': FaTwitter,
    'figma.com': SiFigma,
    'clickup.com': SiClickup,
    'microsoft.com': FaMicrosoft,
  };

  return iconMap[appKey] || Search;
};

// Personalized card component that maintains the original UI design
const PersonalizedExampleCard = ({
  recommendation,
  index,
  onPromptClick
}: {
  recommendation: PersonalizedPromptRecommendation;
  index: number;
  onPromptClick: (recommendation: PersonalizedPromptRecommendation) => void;
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, {
    once: true,
    margin: "0px 0px -20% 0px"
  });

  // Helper function to get the first sentence from the query
  const getFirstSentence = (query: string) => {
    // Find the first sentence ending with '.', '!', or '?'
    const match = query.match(/^[^.!?]*[.!?]/);
    if (match) {
      return match[0].trim();
    }

    // If no sentence ending found, truncate at a reasonable length
    if (query.length <= 80) return query;
    const truncated = query.substring(0, 80);
    const lastSpace = truncated.lastIndexOf(' ');
    return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
  };

  const { prompt, canRun } = recommendation;

  return (
    <motion.div
      ref={cardRef}
      initial={{
        opacity: 0,
        y: 20,
        scale: 0.95
      }}
      animate={isInView ? {
        opacity: 1,
        y: 0,
        scale: 1
      } : {
        opacity: 0,
        y: 20,
        scale: 0.95
      }}
      transition={{
        duration: 0.4,
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{
        scale: 1.02,
        y: -3,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
    >
      <Card
        className="cursor-pointer h-full bg-muted/50 dark:bg-muted/30 border border-border min-h-[45px] transition-shadow duration-300 hover:shadow-lg"
        onClick={() => onPromptClick(recommendation)}
      >
        <CardContent className="px-3 py-0.5 h-full flex flex-col space-y-0.5">
          {/* Quote snippet at the top - single line */}
          <div>
            <blockquote className="text-xs text-muted-foreground italic leading-tight truncate">
              "{getFirstSentence(recommendation.personalizedQuery)}"
            </blockquote>
          </div>

          {/* Title */}
          <CardTitle className="font-semibold text-foreground text-sm leading-tight">
            {prompt.title}
          </CardTitle>

          {/* Integration icons tray with overlapping circular design */}
          <div className="flex items-center justify-between mt-auto pt-1">
            <div className="flex items-center">
              {prompt.integrations.slice(0, 5).map((integration, idx) => {
                const IconComponent = getIntegrationIcon(integration.app_key);
                return (
                  <div
                    key={integration.app_key}
                    className="relative flex items-center justify-center bg-background rounded-full shadow-sm p-1"
                    style={{
                      height: 24,
                      width: 24,
                      marginLeft: idx > 0 ? '-8px' : '0',
                      zIndex: prompt.integrations.length - idx,
                    }}
                    title={INTEGRATION_DISPLAY_NAMES[integration.app_key] || integration.name}
                  >
                    <IconComponent className="w-3 h-3 text-muted-foreground" />
                  </div>
                );
              })}
              {prompt.integrations.length > 5 && (
                <div
                  className="relative flex items-center justify-center bg-muted rounded-full shadow-sm"
                  style={{
                    height: 24,
                    width: 24,
                    marginLeft: '-8px',
                    zIndex: 0,
                  }}
                >
                  <span className="text-xs text-muted-foreground font-medium">
                    +{prompt.integrations.length - 5}
                  </span>
                </div>
              )}
            </div>

            {!canRun && (
              <div className="flex items-center justify-center w-6 h-6">
                <Lock className="h-4 w-4 text-foreground/70" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const Examples = ({
  onSelectPrompt,
  onRequireIntegrations,
}: {
  onSelectPrompt?: (query: string) => void;
  onRequireIntegrations?: (appKeys: string[], promptTitle: string) => void;
}) => {
  const [personalizedPrompts, setPersonalizedPrompts] = useState<PersonalizedPromptRecommendation[]>([]);
  const [allAvailablePrompts, setAllAvailablePrompts] = useState<PersonalizedPromptRecommendation[]>([]);
  const [currentPromptSet, setCurrentPromptSet] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMultipleSets, setHasMultipleSets] = useState(false);

  // Load personalized prompts on mount
  useEffect(() => {
    loadPersonalizedPrompts();
  }, []);

  // Listen for integration updates (reduced frequency)
  useEffect(() => {
    let lastUpdate = 0;
    const UPDATE_COOLDOWN = 10000; // 10 seconds cooldown between updates

    const handleUpdate = () => {
      const now = Date.now();
      if (now - lastUpdate > UPDATE_COOLDOWN) {
        lastUpdate = now;

        // Check if user just returned from OAuth
        const recentlyConnected = localStorage.getItem('composio_recently_connected');
        if (recentlyConnected) {
          console.log('Post-OAuth integration update detected:', recentlyConnected);
          // Use post-OAuth function with retry logic
          setTimeout(() => loadPersonalizedPromptsPostOAuth(), 3000);
          // Clear the flag after handling
          localStorage.removeItem('composio_recently_connected');
        } else {
          // Regular update with shorter delay
          setTimeout(() => loadPersonalizedPrompts(), 1000);
        }
      }
    };

    // Check immediately on mount for post-OAuth scenarios
    const checkOnMount = () => {
      const recentlyConnected = localStorage.getItem('composio_recently_connected');
      if (recentlyConnected) {
        console.log('Post-OAuth detected on mount:', recentlyConnected);
        setTimeout(() => {
          loadPersonalizedPromptsPostOAuth();
          localStorage.removeItem('composio_recently_connected');
        }, 2000);
      }
    };

    checkOnMount();
    window.addEventListener('focus', handleUpdate); // When user returns from OAuth

    return () => window.removeEventListener('focus', handleUpdate);
  }, []);

  const loadPersonalizedPrompts = async (refreshCycle: boolean = false) => {
    try {
      setIsLoading(!refreshCycle); // Don't show loading spinner for refresh cycles
      const result = await getPersonalizedPromptRecommendations();
      const allPrompts = getPromptsToDisplay(result);

      // Store all available prompts for cycling
      setAllAvailablePrompts(allPrompts);

      // Determine if we have enough prompts to show multiple sets
      const hasMultiple = allPrompts.length > 4;
      setHasMultipleSets(hasMultiple);

      if (hasMultiple && refreshCycle) {
        // Cycle to next set of prompts
        const totalSets = Math.ceil(allPrompts.length / 4);
        const nextSet = (currentPromptSet + 1) % totalSets;
        setCurrentPromptSet(nextSet);

        const startIndex = nextSet * 4;
        const endIndex = Math.min(startIndex + 4, allPrompts.length);
        setPersonalizedPrompts(allPrompts.slice(startIndex, endIndex));
      } else {
        // Show first 4 prompts
        setCurrentPromptSet(0);
        setPersonalizedPrompts(allPrompts.slice(0, 4));
      }
    } catch (error) {
      console.error('Error loading personalized prompts:', error);
      setPersonalizedPrompts([]);
      setAllAvailablePrompts([]);
      setHasMultipleSets(false);
    } finally {
      setIsLoading(false);
    }
  };

  const loadPersonalizedPromptsPostOAuth = async () => {
    try {
      console.log('Loading prompts with post-OAuth retry logic...');
      const result = await getPersonalizedPromptRecommendationsPostOAuth();
      const allPrompts = getPromptsToDisplay(result);

      // Store all available prompts for cycling
      setAllAvailablePrompts(allPrompts);

      // Determine if we have enough prompts to show multiple sets
      const hasMultiple = allPrompts.length > 4;
      setHasMultipleSets(hasMultiple);

      // Show first 4 prompts
      setCurrentPromptSet(0);
      setPersonalizedPrompts(allPrompts.slice(0, 4));

      console.log('Post-OAuth prompts loaded successfully');
    } catch (error) {
      console.error('Error loading post-OAuth personalized prompts:', error);
      setPersonalizedPrompts([]);
      setAllAvailablePrompts([]);
      setHasMultipleSets(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadPersonalizedPrompts(true); // Pass true for refresh cycle
    setIsRefreshing(false);
  };

  const handlePromptClick = (recommendation: PersonalizedPromptRecommendation) => {
    if (recommendation.canRun) {
      // User can run this prompt, proceed normally
      onSelectPrompt?.(recommendation.personalizedQuery);
    } else {
      // User needs to connect integrations first
      const missingAppKeys = getMissingIntegrationAppKeys(recommendation.missingIntegrations);
      onRequireIntegrations?.(missingAppKeys, recommendation.prompt.title);
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-end items-center mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="h-8 px-3 text-sm text-muted-foreground hover:text-foreground"
        >
          <motion.div
            animate={{ rotate: isRefreshing ? 360 : 0 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            <RefreshCw size={14} />
          </motion.div>
          <span className="ml-2">
            {hasMultipleSets ? 'More prompts' : 'Refresh'}
          </span>
        </Button>
      </div>
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="h-32 bg-muted/50 rounded-lg animate-pulse" />
          ))}
        </div>
      ) : (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-4"
          key={personalizedPrompts.map(p => p.prompt.id).join('-')}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {personalizedPrompts.map((recommendation, index) => (
            <PersonalizedExampleCard
              key={`${recommendation.prompt.id}-${index}`}
              recommendation={recommendation}
              index={index}
              onPromptClick={handlePromptClick}
            />
          ))}
        </motion.div>
      )}
    </div>
  );
};
